/**
 * AI Configuration for Gemini Flash Lite
 * Optimized for minimal cost and maximum efficiency
 */

import { GenerativeModel, GoogleGenerativeAI, ModelParams } from '@google/generative-ai';

import loadEnv from './loadEnv';

// Load environment variables
loadEnv();

export interface AIConfig {
	apiKey: string;
	model: string;
	maxTokens: number;
	temperature: number;
	topP: number;
	topK: number;
	enableCaching: boolean;
	maxCacheSize: number;
	costTrackingEnabled: boolean;
}

export interface TokenUsage {
	promptTokens: number;
	completionTokens: number;
	totalTokens: number;
	estimatedCost: number;
}

export interface CachedResponse {
	content: string;
	usage: TokenUsage;
	timestamp: Date;
	expiresAt: Date;
}

// AI Configuration with cost optimization
export const aiConfig: AIConfig = {
	apiKey: process.env.GEMINI_API_KEY || "",
	model: "gemini-2.5-flash-lite-preview-06-17", // Gemini Flash Lite for cost efficiency
	maxTokens: 1000, // Keep low for cost optimization
	temperature: 0.1, // Low temperature for consistent, factual outputs
	topP: 0.8,
	topK: 40,
	enableCaching: false, // Disable caching to prevent data mixing between websites
	maxCacheSize: 0, // No cache
	costTrackingEnabled: true,
};

// Gemini Flash Lite pricing (approximate)
export const PRICING = {
	INPUT_TOKEN_COST: 0.000075 / 1000, // $0.075 per 1M input tokens
	OUTPUT_TOKEN_COST: 0.0003 / 1000, // $0.30 per 1M output tokens
};

/**
 * AI Client Manager with cost optimization
 */
export class AIClientManager {
	private client: GoogleGenerativeAI;
	private model: GenerativeModel;
	private cache: Map<string, CachedResponse> = new Map();
	private totalCost: number = 0;
	private requestCount: number = 0;

	constructor() {
		if (!aiConfig.apiKey) {
			throw new Error("GEMINI_API_KEY environment variable is required");
		}

		this.client = new GoogleGenerativeAI(aiConfig.apiKey);
		this.model = this.client.getGenerativeModel({
			model: aiConfig.model,
			generationConfig: {
				maxOutputTokens: aiConfig.maxTokens,
				temperature: aiConfig.temperature,
				topP: aiConfig.topP,
				topK: aiConfig.topK,
			},
		});

		// Clear any existing cache to prevent data mixing
		this.clearCache();
	}

	/**
	 * Generate cache key for request with URL to prevent data mixing between websites
	 */
	private generateCacheKey(
		prompt: string,
		context?: string,
		url?: string
	): string {
		// Include URL in cache key to prevent data mixing between different websites
		const content = url
			? `${url}:${prompt}:${context || ""}`
			: context
			? `${prompt}:${context}`
			: prompt;
		return Buffer.from(content).toString("base64").slice(0, 64);
	}

	/**
	 * Check if cached response is valid
	 */
	private isCacheValid(cached: CachedResponse): boolean {
		return new Date() < cached.expiresAt;
	}

	/**
	 * Calculate estimated cost
	 */
	private calculateCost(
		promptTokens: number,
		completionTokens: number
	): number {
		const inputCost = promptTokens * PRICING.INPUT_TOKEN_COST;
		const outputCost = completionTokens * PRICING.OUTPUT_TOKEN_COST;
		return inputCost + outputCost;
	}

	/**
	 * Clean expired cache entries
	 */
	private cleanCache(): void {
		const now = new Date();
		for (const [key, cached] of this.cache.entries()) {
			if (now >= cached.expiresAt) {
				this.cache.delete(key);
			}
		}
	}

	/**
	 * Generate AI response with caching and cost tracking
	 */
	async generateResponse(
		prompt: string,
		context?: string,
		options: {
			useCache?: boolean;
			maxRetries?: number;
			cacheExpiryMinutes?: number;
			url?: string; // Add URL to prevent cache mixing between websites
		} = {}
	): Promise<{ content: string; usage: TokenUsage; fromCache: boolean }> {
		const {
			useCache = aiConfig.enableCaching,
			maxRetries = 3,
			cacheExpiryMinutes = 60,
			url,
		} = options;

		// Generate cache key with URL to prevent data mixing between websites
		const cacheKey = this.generateCacheKey(prompt, context, url);

		// Check cache first
		if (useCache && this.cache.has(cacheKey)) {
			const cached = this.cache.get(cacheKey)!;
			if (this.isCacheValid(cached)) {
				return {
					content: cached.content,
					usage: cached.usage,
					fromCache: true,
				};
			} else {
				this.cache.delete(cacheKey);
			}
		}

		// Prepare full prompt
		const fullPrompt = context
			? `Context: ${context}\n\nTask: ${prompt}`
			: prompt;

		let lastError: Error | null = null;

		for (let attempt = 1; attempt <= maxRetries; attempt++) {
			try {
				const result = await this.model.generateContent(fullPrompt);
				const response = await result.response;
				const content = response.text();

				// Estimate token usage (Gemini doesn't provide exact counts)
				const promptTokens = Math.ceil(fullPrompt.length / 4); // Rough estimation
				const completionTokens = Math.ceil(content.length / 4);
				const totalTokens = promptTokens + completionTokens;
				const estimatedCost = this.calculateCost(
					promptTokens,
					completionTokens
				);

				const usage: TokenUsage = {
					promptTokens,
					completionTokens,
					totalTokens,
					estimatedCost,
				};

				// Update cost tracking
				this.totalCost += estimatedCost;
				this.requestCount++;

				// Cache the response
				if (useCache && this.cache.size < aiConfig.maxCacheSize) {
					const expiresAt = new Date();
					expiresAt.setMinutes(
						expiresAt.getMinutes() + cacheExpiryMinutes
					);

					this.cache.set(cacheKey, {
						content,
						usage,
						timestamp: new Date(),
						expiresAt,
					});
				}

				// Clean cache periodically
				if (this.requestCount % 100 === 0) {
					this.cleanCache();
				}

				return {
					content,
					usage,
					fromCache: false,
				};
			} catch (error) {
				lastError = error as Error;
				if (attempt < maxRetries) {
					// Wait before retry with exponential backoff
					await new Promise((resolve) =>
						setTimeout(resolve, Math.pow(2, attempt) * 1000)
					);
				}
			}
		}

		throw new Error(
			`AI request failed after ${maxRetries} attempts: ${lastError?.message}`
		);
	}

	/**
	 * Get cost statistics
	 */
	getCostStats(): {
		totalCost: number;
		requestCount: number;
		averageCostPerRequest: number;
		cacheHitRate: number;
		cacheSize: number;
	} {
		const cacheHits = Array.from(this.cache.values()).length;
		const cacheHitRate =
			this.requestCount > 0 ? (cacheHits / this.requestCount) * 100 : 0;

		return {
			totalCost: this.totalCost,
			requestCount: this.requestCount,
			averageCostPerRequest:
				this.requestCount > 0 ? this.totalCost / this.requestCount : 0,
			cacheHitRate,
			cacheSize: this.cache.size,
		};
	}

	/**
	 * Clear cache
	 */
	clearCache(): void {
		this.cache.clear();
	}

	/**
	 * Reset cost tracking
	 */
	resetCostTracking(): void {
		this.totalCost = 0;
		this.requestCount = 0;
	}
}

// Export singleton instance
export const aiClient = new AIClientManager();
export default aiClient;
