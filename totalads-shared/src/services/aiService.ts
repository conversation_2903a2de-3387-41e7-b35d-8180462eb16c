/**
 * AI Service for Business Intelligence Extraction
 * Optimized for scraping enhancement with minimal cost
 */

import { aiClient, TokenUsage } from '../config/ai';

export interface BusinessIntelligence {
	companyType: string;
	industry: string[];
	businessModel: string;
	targetMarket: string[];
	keyServices: string[];
	competitiveAdvantages: string[];
	marketPosition:
		| "startup"
		| "small"
		| "medium"
		| "large"
		| "enterprise"
		| "unknown";
	fundingStage?: string;
	revenue?: string;
	employeeCount?: string;
	technologies: string[];
	partnerships: string[];
	certifications: string[];
	awards: string[];
	socialPresence: {
		platforms: string[];
		engagement: "low" | "medium" | "high" | "unknown";
	};
	riskFactors: string[];
	opportunities: string[];
	confidence: number; // 0-100
}

export interface EnhancedContactInfo {
	emails: {
		address: string;
		type: "general" | "sales" | "support" | "hr" | "media" | "unknown";
		confidence: number;
	}[];
	phones: {
		number: string;
		type: "main" | "sales" | "support" | "mobile" | "fax" | "unknown";
		confidence: number;
	}[];
	addresses: {
		address: string;
		type: "headquarters" | "office" | "warehouse" | "store" | "unknown";
		confidence: number;
	}[];
	socialMedia: {
		platform: string;
		url: string;
		verified: boolean;
	}[];
	website: {
		primary: string;
		additional: string[];
	};
}

export interface AIProcessingResult {
	businessIntelligence: BusinessIntelligence;
	enhancedContactInfo: EnhancedContactInfo;
	extractedEntities: {
		people: string[];
		organizations: string[];
		locations: string[];
		products: string[];
	};
	sentiment: {
		overall: "positive" | "neutral" | "negative";
		confidence: number;
	};
	textSummary?: string;
	usage: TokenUsage;
	processingTime: number;
}

/**
 * AI Service for enhanced business data extraction
 */
export class AIService {
	private static instance: AIService;

	private constructor() {}

	public static getInstance(): AIService {
		if (!AIService.instance) {
			AIService.instance = new AIService();
		}
		return AIService.instance;
	}

	/**
	 * Extract business intelligence from scraped content
	 */
	async extractBusinessIntelligence(
		content: string,
		companyName?: string,
		websiteUrl?: string
	): Promise<BusinessIntelligence> {
		const startTime = Date.now();

		// Optimize content length but keep more content for better analysis
		const optimizedContent = this.optimizeContentForAI(content, 4000);

		const prompt = `
You are an expert business analyst. Analyze the following company content and extract comprehensive business intelligence. Extract EVERY possible piece of information, making intelligent inferences when needed.

Company: ${companyName || "Unknown"}
Website: ${websiteUrl || "Unknown"}

ENHANCED ANALYSIS INSTRUCTIONS:
1. Extract ALL available information from the content - be comprehensive
2. Make intelligent inferences based on context clues and industry knowledge
3. Look for implicit information (e.g., "enterprise clients" = B2B model, "global offices" = large company)
4. Analyze language, tone, and positioning to determine market position and target audience
5. Infer company size from: global presence, office locations, service scope, client types, team size
6. Extract technologies from any technical mentions, integrations, platforms, tools
7. Identify partnerships from "partners", "integrations", "works with", "powered by" language
8. Look for awards, certifications, recognition, rankings, compliance standards
9. Analyze social media links and engagement indicators from follower counts/activity
10. Assess risks and opportunities based on business model, market trends, and competitive landscape
11. Extract founder information, company history, funding mentions
12. Identify competitive advantages from unique features, proprietary technology, market position

Required JSON structure:
{
  "companyType": "corporation|llc|partnership|startup|enterprise|saas|unknown",
  "industry": ["primary industry", "secondary industries", "sub-sectors"],
  "businessModel": "B2B|B2C|B2B2C|marketplace|saas|freemium|subscription|unknown",
  "targetMarket": ["specific target segments", "company sizes", "industries served"],
  "keyServices": ["all services/products mentioned", "core offerings", "additional services"],
  "competitiveAdvantages": ["unique selling points", "differentiators", "strengths mentioned"],
  "marketPosition": "startup|small|medium|large|enterprise",
  "fundingStage": "bootstrap|seed|series-a|series-b|series-c|ipo|acquired|unknown",
  "revenue": "estimate based on size/scope or null",
  "employeeCount": "estimate based on scope/global presence or null",
  "technologies": ["all tech mentioned", "platforms", "integrations", "tools"],
  "partnerships": ["partner companies", "integrations", "collaborations"],
  "certifications": ["industry certifications", "compliance standards", "accreditations"],
  "awards": ["awards mentioned", "recognition", "rankings", "achievements"],
  "socialPresence": {
    "platforms": ["all social platforms found"],
    "engagement": "low|medium|high based on follower counts/activity"
  },
  "riskFactors": ["competition risks", "market risks", "business model risks"],
  "opportunities": ["growth opportunities", "market expansion", "new features"],
  "confidence": number (0-100)
}

IMPORTANT:
- DO NOT leave arrays empty unless truly no information exists
- Make educated inferences based on available data
- For company size, consider: global presence, office locations, service scope, client types
- For technologies, include any platforms, tools, or tech stack mentioned
- For partnerships, look for "integrates with", "partners", "works with" language
- Extract social media platforms from any social links found
- Be comprehensive - extract every piece of relevant business information
`;

		try {
			const result = await aiClient.generateResponse(
				prompt,
				optimizedContent,
				{
					useCache: false, // Disable caching to prevent data mixing
					url: websiteUrl,
				}
			);

			// Extract JSON from markdown code blocks if present
			const cleanedContent = this.extractJsonFromResponse(result.content);

			// Validate JSON before parsing
			if (!cleanedContent || cleanedContent.trim().length === 0) {
				console.warn(
					"Empty response from AI for business intelligence"
				);
				return this.getDefaultBusinessIntelligence();
			}

			try {
				const businessIntelligence = JSON.parse(
					cleanedContent
				) as BusinessIntelligence;

				// Validate and sanitize the result
				return this.validateBusinessIntelligence(businessIntelligence);
			} catch (parseError) {
				console.error(
					"JSON parse error for business intelligence:",
					parseError
				);
				console.error("Raw AI response:", result.content);
				console.error("Cleaned content:", cleanedContent);
				return this.getDefaultBusinessIntelligence();
			}
		} catch (error) {
			console.error("Error extracting business intelligence:", error);
			return this.getDefaultBusinessIntelligence();
		}
	}

	/**
	 * Enhance contact information using AI
	 */
	async enhanceContactInfo(
		rawContactData: any,
		content: string,
		websiteUrl?: string
	): Promise<EnhancedContactInfo> {
		const optimizedContent = this.optimizeContentForAI(content, 3000);

		const prompt = `
You are an expert contact information analyst. Analyze the content thoroughly and extract ALL possible contact information, including social media links, addresses, and communication channels.

Raw contact data: ${JSON.stringify(rawContactData)}

INSTRUCTIONS:
1. Extract ALL email addresses from the content (look for @domain patterns)
2. Find ALL phone numbers (various formats: +1-xxx-xxx-xxxx, (xxx) xxx-xxxx, etc.)
3. Identify ALL physical addresses (street addresses, office locations, PO boxes)
4. Extract ALL social media links and platforms
5. Find contact forms, chat widgets, or other communication methods
6. Look for department-specific contacts (sales@, support@, info@, etc.)
7. Identify regional offices or multiple locations
8. Extract any additional websites or subdomains
9. Classify each contact method by type and purpose
10. Assign confidence scores based on context and clarity

Required JSON structure:
{
  "emails": [{"address": "<EMAIL>", "type": "general|sales|support|hr|media|info|contact|unknown", "confidence": number}],
  "phones": [{"number": "formatted number", "type": "main|sales|support|mobile|fax|toll-free|international|unknown", "confidence": number}],
  "addresses": [{"address": "full address", "type": "headquarters|office|warehouse|store|regional|mailing|unknown", "confidence": number}],
  "socialMedia": [{"platform": "facebook|twitter|linkedin|instagram|youtube|pinterest|tiktok|other", "url": "full url", "verified": boolean}],
  "website": {"primary": "main website", "additional": ["subdomains", "related sites", "landing pages"]}
}

IMPORTANT:
- Extract social media platforms from ANY social links found in the content
- Look for patterns like "Follow us on", social icons, footer links
- Include ALL variations of contact information found
- For addresses, include office locations mentioned in "About", "Contact", or "Locations" sections
- Classify email types based on prefix (sales@, support@, info@, contact@, etc.)
- Confidence should reflect how certain you are about the classification
- DO NOT leave arrays empty if ANY contact information exists in the content
`;

		try {
			const result = await aiClient.generateResponse(
				prompt,
				optimizedContent,
				{
					useCache: false, // Disable caching to prevent data mixing
					url: websiteUrl,
				}
			);

			// Extract JSON from markdown code blocks if present
			const cleanedContent = this.extractJsonFromResponse(result.content);

			// Validate JSON before parsing
			if (!cleanedContent || cleanedContent.trim().length === 0) {
				console.warn(
					"Empty response from AI for contact info enhancement"
				);
				return this.getDefaultContactInfo();
			}

			try {
				const parsed = JSON.parse(
					cleanedContent
				) as EnhancedContactInfo;
				// Validate the parsed result has the expected structure
				return this.validateContactInfo(parsed);
			} catch (parseError) {
				console.error("JSON parse error for contact info:", parseError);
				console.error("Raw AI response:", result.content);
				console.error("Cleaned content:", cleanedContent);

				// Try one more time with aggressive JSON repair
				try {
					const repairedJson =
						this.aggressiveJsonRepair(cleanedContent);
					const parsed = JSON.parse(
						repairedJson
					) as EnhancedContactInfo;
					console.log("Successfully repaired JSON for contact info");
					return this.validateContactInfo(parsed);
				} catch (repairError) {
					console.error("Failed to repair JSON:", repairError);
					return this.getDefaultContactInfo();
				}
			}
		} catch (error) {
			console.error("Error enhancing contact info:", error);
			return this.getDefaultContactInfo();
		}
	}

	/**
	 * Extract entities and sentiment from content
	 */
	async extractEntitiesAndSentiment(
		content: string,
		websiteUrl?: string
	): Promise<{
		entities: {
			people: string[];
			organizations: string[];
			locations: string[];
			products: string[];
		};
		sentiment: {
			overall: "positive" | "neutral" | "negative";
			confidence: number;
		};
	}> {
		const optimizedContent = this.optimizeContentForAI(content, 2500);

		const prompt = `
You are an expert entity extraction and sentiment analysis specialist. Extract ALL relevant entities and perform comprehensive sentiment analysis.

INSTRUCTIONS:
1. Extract ALL person names mentioned (executives, founders, team members, customers, partners)
2. Identify ALL organizations (partners, clients, competitors, subsidiaries, parent companies)
3. Find ALL locations (headquarters, offices, regions served, markets)
4. Extract ALL products and services (current offerings, features, tools, solutions)
5. Analyze overall sentiment considering: tone, language, positioning, customer focus
6. Look for industry-specific terminology and entities
7. Include brand names, product names, and service names
8. Extract geographic markets and regions
9. Identify technology partners and integrations

Required JSON structure:
{
  "entities": {
    "people": ["executives", "founders", "team members", "quoted individuals"],
    "organizations": ["partners", "clients", "competitors", "integrations", "subsidiaries"],
    "locations": ["headquarters", "offices", "regions", "countries", "cities"],
    "products": ["main products", "features", "tools", "services", "solutions", "platforms"]
  },
  "sentiment": {
    "overall": "positive|neutral|negative",
    "confidence": number (0-100)
  }
}

IMPORTANT:
- Extract entities from ALL parts of the content (headers, body, footer, navigation)
- Include product features as separate entities if they're prominently mentioned
- For locations, include both physical locations and market regions
- For organizations, include any company names mentioned in partnerships, integrations, or case studies
- For people, look in "About", "Team", "Leadership", testimonials, or quotes
- Sentiment should consider: innovation language, customer focus, growth indicators, market positioning
- DO NOT limit to 10 items - extract ALL relevant entities found
- Be comprehensive and thorough in extraction
`;

		try {
			const result = await aiClient.generateResponse(
				prompt,
				optimizedContent,
				{
					useCache: false, // Disable caching to prevent data mixing
					url: websiteUrl,
				}
			);

			// Extract JSON from markdown code blocks if present
			const cleanedContent = this.extractJsonFromResponse(result.content);

			// Validate JSON before parsing
			if (!cleanedContent || cleanedContent.trim().length === 0) {
				console.warn(
					"Empty response from AI for entities and sentiment"
				);
				return {
					entities: {
						people: [],
						organizations: [],
						locations: [],
						products: [],
					},
					sentiment: { overall: "neutral", confidence: 0 },
				};
			}

			try {
				return JSON.parse(cleanedContent);
			} catch (parseError) {
				console.error(
					"JSON parse error for entities and sentiment:",
					parseError
				);
				console.error("Raw AI response:", result.content);
				console.error("Cleaned content:", cleanedContent);
				return {
					entities: {
						people: [],
						organizations: [],
						locations: [],
						products: [],
					},
					sentiment: { overall: "neutral", confidence: 0 },
				};
			}
		} catch (error) {
			console.error("Error extracting entities and sentiment:", error);
			return {
				entities: {
					people: [],
					organizations: [],
					locations: [],
					products: [],
				},
				sentiment: { overall: "neutral", confidence: 0 },
			};
		}
	}

	/**
	 * Summarize text content into 5-8 key business points
	 */
	async summarizeBusinessText(
		content: string,
		companyName?: string,
		websiteUrl?: string
	): Promise<string> {
		const optimizedContent = this.optimizeContentForAI(content, 3000);

		const prompt = `
You are an expert business content summarizer. Create a concise 5-8 line summary of the key business points from the provided content.

Company: ${companyName || "Unknown"}

SUMMARY REQUIREMENTS:
- Extract 5-8 most important business points
- Focus on: services, value propositions, target market, competitive advantages, key features
- Each point should be 1 clear, concise sentence
- Prioritize actionable business intelligence
- Remove redundant or marketing fluff
- Make it sound professional and informative
- Focus on what makes this business unique and valuable

Format as numbered list:
1. [Key business point]
2. [Key business point]
...

Content to summarize:
`;

		try {
			const result = await aiClient.generateResponse(
				prompt,
				optimizedContent,
				{
					useCache: false, // Disable caching to prevent data mixing
					url: websiteUrl,
				}
			);

			return result.content.trim();
		} catch (error) {
			console.error("Error summarizing business text:", error);
			// Return a basic summary if AI fails
			const lines = content
				.split("\n")
				.filter((line) => line.trim().length > 50);
			return lines.slice(0, 5).join("\n");
		}
	}

	/**
	 * Process complete business analysis
	 */
	async processBusinessAnalysis(
		content: string,
		rawContactData: any,
		companyName?: string,
		websiteUrl?: string
	): Promise<AIProcessingResult> {
		const startTime = Date.now();

		try {
			// Run all AI analyses in parallel for efficiency
			const [
				businessIntelligence,
				enhancedContactInfo,
				entitiesAndSentiment,
				textSummary,
			] = await Promise.all([
				this.extractBusinessIntelligence(
					content,
					companyName,
					websiteUrl
				),
				this.enhanceContactInfo(rawContactData, content, websiteUrl),
				this.extractEntitiesAndSentiment(content, websiteUrl),
				this.summarizeBusinessText(content, companyName, websiteUrl),
			]);

			const processingTime = Date.now() - startTime;

			return {
				businessIntelligence,
				enhancedContactInfo,
				extractedEntities: entitiesAndSentiment.entities,
				sentiment: entitiesAndSentiment.sentiment,
				textSummary,
				usage: {
					promptTokens: 0, // Will be calculated by aiClient
					completionTokens: 0,
					totalTokens: 0,
					estimatedCost: 0,
				},
				processingTime,
			};
		} catch (error) {
			console.error("Error in business analysis:", error);
			throw error;
		}
	}

	/**
	 * Extract JSON from AI response, handling markdown code blocks and malformed JSON
	 */
	private extractJsonFromResponse(content: string): string {
		if (!content || content.trim().length === 0) {
			return "{}";
		}

		// Remove markdown code blocks if present
		const jsonMatch = content.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
		if (jsonMatch) {
			return this.cleanJsonString(jsonMatch[1]);
		}

		// If no code blocks, try to find JSON object
		const jsonObjectMatch = content.match(/\{[\s\S]*\}/);
		if (jsonObjectMatch) {
			return this.cleanJsonString(jsonObjectMatch[0]);
		}

		// Return empty object if no JSON found
		return "{}";
	}

	/**
	 * Clean and fix common JSON formatting issues
	 */
	private cleanJsonString(jsonStr: string): string {
		if (!jsonStr) return "{}";

		let cleaned = jsonStr.trim();

		// Remove any non-JSON content before the first {
		const firstBrace = cleaned.indexOf("{");
		if (firstBrace > 0) {
			cleaned = cleaned.substring(firstBrace);
		}

		// Remove any non-JSON content after the last }
		const lastBrace = cleaned.lastIndexOf("}");
		if (lastBrace >= 0 && lastBrace < cleaned.length - 1) {
			cleaned = cleaned.substring(0, lastBrace + 1);
		}

		// Fix malformed property syntax - fix "property"]: value to "property": value
		cleaned = cleaned.replace(/("[\w\s]+")]\s*:/g, "$1:");

		// Fix incomplete arrays - if we find an unclosed array, close it
		cleaned = this.fixIncompleteArrays(cleaned);

		// Fix common JSON issues
		// Remove trailing commas before closing brackets/braces
		cleaned = cleaned.replace(/,(\s*[}\]])/g, "$1");

		// Fix unescaped quotes in strings (basic fix)
		cleaned = cleaned.replace(
			/:\s*"([^"]*)"([^",}\]]*)"([^",}\]]*)"([^",}\]]*)/g,
			': "$1\\"$2\\"$3"'
		);

		// Ensure proper array formatting
		cleaned = cleaned.replace(/\[\s*,/g, "[");
		cleaned = cleaned.replace(/,\s*,/g, ",");

		// Fix missing colons after property names
		cleaned = cleaned.replace(
			/("[\w\s]+")(\s*)(\d+|true|false|null|\[|\{)/g,
			"$1:$2$3"
		);

		return cleaned;
	}

	/**
	 * Fix incomplete arrays in JSON strings
	 */
	private fixIncompleteArrays(jsonStr: string): string {
		let fixed = jsonStr;

		// First, fix any truncated arrays that end abruptly
		// Look for patterns like: "additional": ["https://www.html", "https://www.html", "
		const truncatedArrayPattern = /"([^"]+)":\s*\[[^\]]*"$/;
		if (truncatedArrayPattern.test(fixed)) {
			// Find the last incomplete string and close the array
			const lastQuoteIndex = fixed.lastIndexOf('"');
			if (lastQuoteIndex > 0) {
				fixed = fixed.substring(0, lastQuoteIndex + 1) + "]";
			}
		}

		// Find all array starts
		const arrayPattern = /"([^"]+)":\s*\[/g;
		let match;
		const arrayPositions: { key: string; start: number; end?: number }[] =
			[];

		while ((match = arrayPattern.exec(jsonStr)) !== null) {
			arrayPositions.push({
				key: match[1],
				start: match.index + match[0].length - 1, // Position of '['
			});
		}

		// For each array, find its closing bracket
		for (const arrayPos of arrayPositions) {
			let bracketCount = 1;
			let pos = arrayPos.start + 1;
			let foundEnd = false;

			while (pos < fixed.length && bracketCount > 0) {
				const char = fixed[pos];
				if (char === "[") bracketCount++;
				else if (char === "]") bracketCount--;

				if (bracketCount === 0) {
					arrayPos.end = pos;
					foundEnd = true;
					break;
				}
				pos++;
			}

			// If array is not properly closed, fix it
			if (!foundEnd) {
				// Find the last complete element in the array
				const arrayContent = fixed.substring(arrayPos.start + 1);
				const lastCommaIndex = arrayContent.lastIndexOf(",");
				const lastQuoteIndex = arrayContent.lastIndexOf('"');
				const lastBraceIndex = arrayContent.lastIndexOf("}");

				// Determine the best place to close the array
				let insertPos = arrayPos.start + 1;

				if (lastBraceIndex > Math.max(lastCommaIndex, lastQuoteIndex)) {
					// We have a complete object, close after it
					insertPos = arrayPos.start + 1 + lastBraceIndex + 1;
				} else if (lastQuoteIndex > lastCommaIndex) {
					// We have an incomplete string, close it and the array
					insertPos = arrayPos.start + 1 + lastQuoteIndex + 1;
				} else if (lastCommaIndex >= 0) {
					// Remove trailing comma and close array
					insertPos = arrayPos.start + 1 + lastCommaIndex;
					fixed =
						fixed.substring(0, insertPos) +
						"]" +
						fixed.substring(insertPos + 1);
					break;
				}

				// Insert closing bracket
				fixed =
					fixed.substring(0, insertPos) +
					"]" +
					fixed.substring(insertPos);
				break; // Only fix the first incomplete array to avoid position shifts
			}
		}

		return fixed;
	}

	/**
	 * Optimize content length for AI processing to reduce costs
	 */
	private optimizeContentForAI(content: string, maxLength: number): string {
		if (content.length <= maxLength) {
			return content;
		}

		// Enhanced business keywords for better content selection
		const businessKeywords = [
			// Core business terms
			"company",
			"business",
			"service",
			"product",
			"industry",
			"market",
			"customer",
			"client",
			"solution",
			"technology",
			"innovation",
			"team",
			"about",
			"mission",
			"vision",
			"value",
			"experience",

			// Contact and location terms
			"contact",
			"email",
			"phone",
			"address",
			"office",
			"headquarters",
			"location",
			"call",
			"reach",
			"support",
			"sales",
			"info",

			// Social and partnership terms
			"partner",
			"integration",
			"social",
			"follow",
			"connect",
			"linkedin",
			"twitter",
			"facebook",
			"instagram",
			"youtube",
			"collaboration",

			// Business intelligence terms
			"award",
			"certification",
			"recognition",
			"achievement",
			"founded",
			"established",
			"revenue",
			"employee",
			"staff",
			"global",
			"international",
			"enterprise",
			"startup",
			"funding",
			"investment",
			"acquisition",

			// Technology and features
			"platform",
			"software",
			"tool",
			"feature",
			"api",
			"integration",
			"cloud",
			"saas",
			"analytics",
			"data",
			"ai",
			"machine learning",

			// Market and competitive terms
			"leader",
			"leading",
			"competitive",
			"advantage",
			"unique",
			"best",
			"top",
			"premier",
			"trusted",
			"proven",
			"expert",
			"specialist",
		];

		// Split content into sentences
		const sentences = content.split(/[.!?]+/);

		// Score sentences based on business relevance with enhanced scoring
		const scoredSentences = sentences.map((sentence) => {
			const lowerSentence = sentence.toLowerCase();
			let score = 0;

			// Base keyword scoring
			businessKeywords.forEach((keyword) => {
				if (lowerSentence.includes(keyword)) {
					score += 1;
				}
			});

			// Bonus scoring for specific patterns
			if (lowerSentence.includes("@") && lowerSentence.includes("."))
				score += 3; // Email patterns
			if (
				lowerSentence.match(
					/\+?\d{1,3}[-.\s]?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/
				)
			)
				score += 3; // Phone patterns
			if (
				lowerSentence.includes("http") ||
				lowerSentence.includes("www.")
			)
				score += 2; // URL patterns
			if (
				lowerSentence.includes("founded") ||
				lowerSentence.includes("established")
			)
				score += 2; // Company history
			if (
				lowerSentence.includes("million") ||
				lowerSentence.includes("billion")
			)
				score += 2; // Financial info
			if (
				lowerSentence.includes("employees") ||
				lowerSentence.includes("team of")
			)
				score += 2; // Size info

			// Length bonus for substantial sentences
			if (sentence.length > 50) score += 1;
			if (sentence.length > 100) score += 1;

			return {
				sentence: sentence.trim(),
				score,
				length: sentence.length,
			};
		});

		// Sort by score (descending) and take top sentences
		scoredSentences.sort((a, b) => b.score - a.score);

		let optimizedContent = "";
		let currentLength = 0;

		// First pass: Add high-scoring sentences
		for (const item of scoredSentences) {
			if (item.score > 0 && currentLength + item.length <= maxLength) {
				optimizedContent += item.sentence + ". ";
				currentLength += item.length + 2;
			}
		}

		// Second pass: Fill remaining space with any remaining content
		if (currentLength < maxLength * 0.8) {
			for (const item of scoredSentences) {
				if (
					item.score === 0 &&
					currentLength + item.length <= maxLength
				) {
					optimizedContent += item.sentence + ". ";
					currentLength += item.length + 2;
				}
			}
		}

		return optimizedContent.trim();
	}

	/**
	 * Validate and sanitize business intelligence data
	 */
	private validateBusinessIntelligence(data: any): BusinessIntelligence {
		return {
			companyType: data.companyType || "unknown",
			industry: Array.isArray(data.industry) ? data.industry : [],
			businessModel: data.businessModel || "unknown",
			targetMarket: Array.isArray(data.targetMarket)
				? data.targetMarket
				: [],
			keyServices: Array.isArray(data.keyServices)
				? data.keyServices
				: [],
			competitiveAdvantages: Array.isArray(data.competitiveAdvantages)
				? data.competitiveAdvantages
				: [],
			marketPosition: [
				"startup",
				"small",
				"medium",
				"large",
				"enterprise",
			].includes(data.marketPosition)
				? data.marketPosition
				: "unknown",
			fundingStage: data.fundingStage || null,
			revenue: data.revenue || null,
			employeeCount: data.employeeCount || null,
			technologies: Array.isArray(data.technologies)
				? data.technologies
				: [],
			partnerships: Array.isArray(data.partnerships)
				? data.partnerships
				: [],
			certifications: Array.isArray(data.certifications)
				? data.certifications
				: [],
			awards: Array.isArray(data.awards) ? data.awards : [],
			socialPresence: {
				platforms: Array.isArray(data.socialPresence?.platforms)
					? data.socialPresence.platforms
					: [],
				engagement: ["low", "medium", "high"].includes(
					data.socialPresence?.engagement
				)
					? data.socialPresence.engagement
					: "unknown",
			},
			riskFactors: Array.isArray(data.riskFactors)
				? data.riskFactors
				: [],
			opportunities: Array.isArray(data.opportunities)
				? data.opportunities
				: [],
			confidence:
				typeof data.confidence === "number"
					? Math.max(0, Math.min(100, data.confidence))
					: 0,
		};
	}

	/**
	 * Get default business intelligence structure
	 */
	private getDefaultBusinessIntelligence(): BusinessIntelligence {
		return {
			companyType: "unknown",
			industry: [],
			businessModel: "unknown",
			targetMarket: [],
			keyServices: [],
			competitiveAdvantages: [],
			marketPosition: "unknown",
			technologies: [],
			partnerships: [],
			certifications: [],
			awards: [],
			socialPresence: { platforms: [], engagement: "unknown" },
			riskFactors: [],
			opportunities: [],
			confidence: 0,
		};
	}

	/**
	 * Get default contact info structure
	 */
	private getDefaultContactInfo(): EnhancedContactInfo {
		return {
			emails: [],
			phones: [],
			addresses: [],
			socialMedia: [],
			website: { primary: "", additional: [] },
		};
	}

	/**
	 * Validate and sanitize contact info data
	 */
	private validateContactInfo(data: any): EnhancedContactInfo {
		return {
			emails: Array.isArray(data.emails)
				? data.emails.filter(
						(email: any) =>
							email &&
							typeof email.address === "string" &&
							email.address.includes("@")
				  )
				: [],
			phones: Array.isArray(data.phones)
				? data.phones.filter(
						(phone: any) =>
							phone &&
							typeof phone.number === "string" &&
							phone.number.trim().length > 0
				  )
				: [],
			addresses: Array.isArray(data.addresses)
				? data.addresses.filter(
						(addr: any) =>
							addr &&
							typeof addr.address === "string" &&
							addr.address.trim().length > 0
				  )
				: [],
			socialMedia: Array.isArray(data.socialMedia)
				? data.socialMedia.filter(
						(social: any) =>
							social &&
							typeof social.url === "string" &&
							social.url.startsWith("http")
				  )
				: [],
			website: {
				primary:
					typeof data.website?.primary === "string"
						? data.website.primary
						: "",
				additional: Array.isArray(data.website?.additional)
					? data.website.additional.filter(
							(url: any) =>
								typeof url === "string" &&
								url.startsWith("http")
					  )
					: [],
			},
		};
	}

	/**
	 * Aggressive JSON repair for malformed responses
	 */
	private aggressiveJsonRepair(jsonStr: string): string {
		if (!jsonStr) return "{}";

		let repaired = jsonStr.trim();

		// Fix common malformed patterns
		// Fix "property"]: value to "property": value
		repaired = repaired.replace(/("[\w\s]+")]\s*:/g, "$1:");

		// Fix missing colons after property names
		repaired = repaired.replace(
			/("[\w\s]+")(\s+)(\d+|true|false|null|\[|\{|")/g,
			"$1:$2$3"
		);

		// Fix incomplete strings at end of arrays
		repaired = repaired.replace(/,\s*"[^"]*$/g, "");

		// Fix repeated array elements (like multiple "https://www.html")
		repaired = repaired.replace(
			/("https:\/\/www\.html",?\s*){2,}/g,
			'"https://www.html"'
		);

		// Fix incomplete objects in arrays - add missing closing braces
		// Pattern: "property": value}] should be "property": value}]
		repaired = repaired.replace(
			/(\d+|true|false|"[^"]*")\s*\]\s*$/g,
			"$1}]"
		);

		// Fix incomplete arrays - ensure they end with ]
		repaired = repaired.replace(/(\d+|true|false|"[^"]*")\s*$/g, "$1}]");

		// Fix missing closing braces for objects in arrays
		// Look for patterns like: "confidence": 1.0\n    }] and fix to "confidence": 1.0\n    }]
		repaired = repaired.replace(
			/("[\w\s]+"\s*:\s*(?:\d+(?:\.\d+)?|true|false|"[^"]*"))\s*\]\s*$/gm,
			"$1}]"
		);

		// Remove trailing incomplete content but preserve complete structures
		const lastCompleteArray = repaired.lastIndexOf("]");
		const lastCompleteObject = repaired.lastIndexOf("}");

		if (lastCompleteArray > lastCompleteObject && lastCompleteArray > 0) {
			// If the last complete structure is an array, keep it
			repaired = repaired.substring(0, lastCompleteArray + 1);
		} else if (lastCompleteObject > 0) {
			// Otherwise keep the last complete object
			repaired = repaired.substring(0, lastCompleteObject + 1);
		}

		// Ensure proper JSON structure
		if (!repaired.startsWith("{")) {
			repaired = "{" + repaired;
		}
		if (!repaired.endsWith("}") && !repaired.endsWith("]")) {
			repaired = repaired + "}";
		}

		// Final cleanup - ensure arrays and objects are properly closed
		let openBraces = 0;
		let openBrackets = 0;
		for (let i = 0; i < repaired.length; i++) {
			if (repaired[i] === "{") openBraces++;
			else if (repaired[i] === "}") openBraces--;
			else if (repaired[i] === "[") openBrackets++;
			else if (repaired[i] === "]") openBrackets--;
		}

		// Add missing closing characters
		while (openBrackets > 0) {
			repaired += "]";
			openBrackets--;
		}
		while (openBraces > 0) {
			repaired += "}";
			openBraces--;
		}

		return repaired;
	}
}

// Export singleton instance
export const aiService = AIService.getInstance();
export default aiService;
