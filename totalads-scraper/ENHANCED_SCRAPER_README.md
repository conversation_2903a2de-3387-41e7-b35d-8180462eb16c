# Enhanced Scraper System

## Overview

The enhanced scraper system is designed to efficiently handle multiple concurrent scraping requests while maintaining high performance and reliability. It intelligently routes requests between direct processing and Redis-based queuing based on current load.

## Key Features

### 🚀 Intelligent Request Routing
- **Direct Processing**: For low load scenarios, requests are processed immediately
- **Queue-based Processing**: When load exceeds thresholds, requests are queued using Redis
- **Duplicate Request Handling**: Multiple requests for the same URL are deduplicated

### 📊 Load Management
- **Configurable Concurrency**: Set maximum concurrent requests
- **Queue Threshold**: Automatically switch to queuing when approaching limits
- **Rate Limiting**: Redis-based distributed rate limiting per IP and domain

### 🔄 Robust Error Handling
- **Automatic Retries**: Configurable retry attempts with exponential backoff
- **Circuit Breaker**: Protection against cascading failures
- **Graceful Degradation**: Fallback mechanisms when services are unavailable

## Configuration

### Environment Variables

```bash
# Redis Configuration (uses totalads-shared Redis)
REDIS_URL="redis://127.0.0.1:6379"

# Scraper Configuration
SCRAPER_CONCURRENCY=10                    # Max concurrent direct processing
SCRAPER_MAX_CONCURRENT_REQUESTS=50        # Total max concurrent requests
SCRAPER_QUEUE_THRESHOLD=30                # When to start queuing
SCRAPER_RETRY_ATTEMPTS=3                  # Number of retry attempts
SCRAPER_RETRY_DELAY=2000                  # Base retry delay in ms

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=60000                # Rate limit window (1 minute)
RATE_LIMIT_MAX_REQUESTS=20                # Max requests per window
RATE_LIMIT_DELAY_MS=1000                  # Delay between requests
RATE_LIMIT_BURST_LIMIT=5                  # Burst allowance
```

## API Endpoints

### POST /scrape
Scrape a single URL with intelligent processing.

**Request:**
```json
{
  "url": "https://example.com",
  "enableAI": false
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    // Scraped data
  },
  "meta": {
    "requestId": "uuid-v4",
    "aiEnhanced": false,
    "processingTime": 2500,
    "timestamp": "2024-01-01T12:00:00.000Z"
  }
}
```

### GET /health
Check scraper health and load status.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "service": "totalads-scraper",
  "stats": {
    "activeRequests": 5,
    "internalQueue": 2,
    "redisQueue": {
      "waiting": 0,
      "active": 1,
      "completed": 150,
      "failed": 2
    }
  }
}
```

### GET /stats
Get detailed scraper statistics.

**Response:**
```json
{
  "success": true,
  "data": {
    "activeRequests": 5,
    "internalQueue": 2,
    "redisQueue": {
      "waiting": 0,
      "active": 1,
      "completed": 150,
      "failed": 2
    },
    "configuration": {
      "maxConcurrent": "50",
      "queueThreshold": "30",
      "concurrency": "10"
    }
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

## How It Works

### Request Flow

1. **Request Received**: API receives scraping request
2. **Load Assessment**: Check current active requests and queue size
3. **Route Decision**: 
   - If load < threshold → Direct processing
   - If load ≥ threshold → Queue processing
4. **Processing**: Execute scraping with error handling and retries
5. **Response**: Return results with metadata

### Load Balancing Logic

```typescript
// Simplified logic
const shouldUseQueue = () => {
  const activeCount = activeRequests.size;
  const queueThreshold = parseInt(process.env.SCRAPER_QUEUE_THRESHOLD || '30');
  
  return activeCount >= queueThreshold || existingQueueSize > 0;
};
```

### Rate Limiting

- **Global Rate Limiting**: Per IP address across all requests
- **Domain Rate Limiting**: Per domain to respect website limits
- **Redis-based**: Distributed across multiple instances
- **Headers**: Rate limit information in response headers

## Performance Characteristics

### Throughput
- **Direct Processing**: ~10-50 concurrent requests
- **Queue Processing**: Unlimited queuing with controlled processing
- **Rate Limiting**: Configurable per IP/domain limits

### Latency
- **Direct Processing**: ~2-5 seconds per request
- **Queue Processing**: Variable based on queue size
- **Cache Hits**: Near-instant for duplicate URLs

### Scalability
- **Horizontal**: Multiple instances share Redis queue
- **Vertical**: Configurable concurrency limits
- **Auto-scaling**: Queue-based processing handles spikes

## Monitoring

### Health Checks
- Service availability
- Queue health
- Redis connectivity
- Resource utilization

### Metrics
- Request throughput
- Processing times
- Error rates
- Queue sizes

### Logging
- Request tracking with unique IDs
- Performance metrics
- Error details
- Rate limiting events

## Best Practices

### For High Volume Usage
1. **Monitor Queue Size**: Keep an eye on Redis queue growth
2. **Adjust Thresholds**: Tune concurrency and queue thresholds
3. **Rate Limiting**: Respect target website limits
4. **Error Handling**: Implement proper retry logic

### For Development
1. **Use Health Endpoint**: Monitor service status
2. **Check Stats**: Understand current load
3. **Test Rate Limits**: Verify rate limiting behavior
4. **Monitor Logs**: Track request processing

## Troubleshooting

### Common Issues

1. **High Queue Size**: Increase concurrency or reduce request rate
2. **Rate Limit Errors**: Adjust rate limiting configuration
3. **Redis Connection**: Check Redis connectivity and configuration
4. **Memory Usage**: Monitor and adjust concurrency limits

### Debug Information

- Check `/health` endpoint for service status
- Check `/stats` endpoint for detailed metrics
- Review logs for error patterns
- Monitor Redis queue sizes

## Migration from Previous Version

The enhanced scraper maintains backward compatibility with the existing `/scrape` endpoint while adding intelligent load management and queuing capabilities. No changes are required to existing client code.
