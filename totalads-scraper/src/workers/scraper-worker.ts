#!/usr/bin/env node
/**
 * Background worker for processing scraper jobs
 */
import '../config/loadEnv';
import { scraperQueue } from '../queue/scraper-queue';
import { redisManager } from '../config/redis';
import logger from '../utils/logger';

class ScraperWorker {
  private isShuttingDown = false;

  constructor() {
    this.setupGracefulShutdown();
  }

  /**
   * Start the worker
   */
  async start(): Promise<void> {
    try {
      logger.info('Starting scraper worker...');

      // Test Redis connection
      const redisConnected = await redisManager.testConnection();
      if (!redisConnected) {
        throw new Error('Failed to connect to Redis');
      }

      logger.info('Scraper worker started successfully');
      logger.info('Worker configuration:', {
        concurrency: process.env.QUEUE_CONCURRENCY || '5',
        retryAttempts: process.env.QUEUE_RETRY_ATTEMPTS || '3',
        retryDelay: process.env.QUEUE_RETRY_DELAY || '5000',
      });

      // Keep the process alive
      this.keepAlive();
    } catch (error) {
      logger.error('Failed to start scraper worker:', error);
      process.exit(1);
    }
  }

  /**
   * Keep the worker process alive
   */
  private keepAlive(): void {
    const interval = setInterval(() => {
      if (this.isShuttingDown) {
        clearInterval(interval);
        return;
      }

      // Log worker status periodically
      this.logWorkerStatus();
    }, 30000); // Log every 30 seconds
  }

  /**
   * Log worker status
   */
  private async logWorkerStatus(): Promise<void> {
    try {
      const stats = await scraperQueue.getQueueStats();
      logger.info('Worker status:', {
        scrapeQueue: stats.scrapeQueue,
        bulkScrapeQueue: stats.bulkScrapeQueue,
        redisStatus: redisManager.getConnectionStatus(),
        memoryUsage: process.memoryUsage(),
        uptime: process.uptime(),
      });
    } catch (error) {
      logger.error('Failed to get worker status:', error);
    }
  }

  /**
   * Setup graceful shutdown
   */
  private setupGracefulShutdown(): void {
    const shutdown = async (signal: string) => {
      if (this.isShuttingDown) {
        logger.warn('Force shutdown initiated');
        process.exit(1);
      }

      this.isShuttingDown = true;
      logger.info(`Received ${signal}, shutting down gracefully...`);

      try {
        // Close queue connections
        await scraperQueue.close();
        logger.info('Queue connections closed');

        // Close Redis connections
        await redisManager.closeConnections();
        logger.info('Redis connections closed');

        logger.info('Scraper worker shutdown complete');
        process.exit(0);
      } catch (error) {
        logger.error('Error during shutdown:', error);
        process.exit(1);
      }
    };

    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGUSR2', () => shutdown('SIGUSR2')); // For nodemon

    process.on('uncaughtException', (error) => {
      logger.error('Uncaught exception:', error);
      shutdown('uncaughtException');
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled rejection at:', promise, 'reason:', reason);
      shutdown('unhandledRejection');
    });
  }
}

// Start the worker if this file is run directly
if (require.main === module) {
  const worker = new ScraperWorker();
  worker.start().catch((error) => {
    logger.error('Failed to start worker:', error);
    process.exit(1);
  });
}

export default ScraperWorker;
