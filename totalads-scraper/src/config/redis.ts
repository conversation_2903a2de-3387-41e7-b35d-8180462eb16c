/**
 * Redis configuration and connection management
 */
import Redis from 'ioredis';
import logger from '../utils/logger';

export interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db: number;
  retryDelayOnFailover: number;
  maxRetriesPerRequest: number;
  lazyConnect: boolean;
  keepAlive: number;
  family: number;
  connectTimeout: number;
  commandTimeout: number;
}

const redisConfig: RedisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD || undefined,
  db: parseInt(process.env.REDIS_DB || '0'),
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
  lazyConnect: true,
  keepAlive: 30000,
  family: 4,
  connectTimeout: 10000,
  commandTimeout: 5000,
};

class RedisManager {
  private static instance: RedisManager;
  private client: Redis | null = null;
  private subscriber: Redis | null = null;
  private publisher: Redis | null = null;

  private constructor() {}

  public static getInstance(): RedisManager {
    if (!RedisManager.instance) {
      RedisManager.instance = new RedisManager();
    }
    return RedisManager.instance;
  }

  /**
   * Get the main Redis client
   */
  public getClient(): Redis {
    if (!this.client) {
      this.client = this.createConnection('main');
    }
    return this.client;
  }

  /**
   * Get Redis subscriber client
   */
  public getSubscriber(): Redis {
    if (!this.subscriber) {
      this.subscriber = this.createConnection('subscriber');
    }
    return this.subscriber;
  }

  /**
   * Get Redis publisher client
   */
  public getPublisher(): Redis {
    if (!this.publisher) {
      this.publisher = this.createConnection('publisher');
    }
    return this.publisher;
  }

  /**
   * Create a new Redis connection
   */
  private createConnection(type: string): Redis {
    const redis = new Redis(redisConfig);

    redis.on('connect', () => {
      logger.info(`Redis ${type} client connected`);
    });

    redis.on('ready', () => {
      logger.info(`Redis ${type} client ready`);
    });

    redis.on('error', (error) => {
      logger.error(`Redis ${type} client error:`, error);
    });

    redis.on('close', () => {
      logger.warn(`Redis ${type} client connection closed`);
    });

    redis.on('reconnecting', () => {
      logger.info(`Redis ${type} client reconnecting...`);
    });

    return redis;
  }

  /**
   * Test Redis connection
   */
  public async testConnection(): Promise<boolean> {
    try {
      const client = this.getClient();
      await client.ping();
      logger.info('Redis connection test successful');
      return true;
    } catch (error) {
      logger.error('Redis connection test failed:', error);
      return false;
    }
  }

  /**
   * Close all Redis connections
   */
  public async closeConnections(): Promise<void> {
    const promises: Promise<void>[] = [];

    if (this.client) {
      promises.push(this.client.quit());
      this.client = null;
    }

    if (this.subscriber) {
      promises.push(this.subscriber.quit());
      this.subscriber = null;
    }

    if (this.publisher) {
      promises.push(this.publisher.quit());
      this.publisher = null;
    }

    await Promise.all(promises);
    logger.info('All Redis connections closed');
  }

  /**
   * Get Redis connection status
   */
  public getConnectionStatus(): {
    client: string;
    subscriber: string;
    publisher: string;
  } {
    return {
      client: this.client?.status || 'disconnected',
      subscriber: this.subscriber?.status || 'disconnected',
      publisher: this.publisher?.status || 'disconnected',
    };
  }
}

// Export singleton instance
export const redisManager = RedisManager.getInstance();

// Export Redis client getter for convenience
export const getRedisClient = () => redisManager.getClient();
export const getRedisSubscriber = () => redisManager.getSubscriber();
export const getRedisPublisher = () => redisManager.getPublisher();

export default redisManager;
