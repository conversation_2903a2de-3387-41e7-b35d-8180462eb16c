/**
 * Job queue system for bulk scraping operations using Bull
 */
import Bull, { Job, JobOptions, Queue } from 'bull';
import { getRedisClient } from '../config/redis';
import logger from '../utils/logger';
import { scraperService } from '../core/scraper.service';

export interface ScrapeJobData {
  url: string;
  enableAI?: boolean;
  jobId: string;
  batchId?: string;
  metadata?: Record<string, any>;
  priority?: number;
  retryCount?: number;
}

export interface BulkScrapeJobData {
  urls: string[];
  enableAI?: boolean;
  batchId: string;
  concurrency?: number;
  metadata?: Record<string, any>;
}

export interface ScrapeJobResult {
  url: string;
  success: boolean;
  data?: any;
  error?: string;
  processingTime: number;
  timestamp: number;
}

export interface BulkScrapeProgress {
  total: number;
  completed: number;
  failed: number;
  inProgress: number;
  percentage: number;
}

class ScraperQueueManager {
  private scrapeQueue: Queue<ScrapeJobData>;
  private bulkScrapeQueue: Queue<BulkScrapeJobData>;
  private redis;

  constructor() {
    this.redis = getRedisClient();
    
    // Initialize queues
    this.scrapeQueue = new Bull<ScrapeJobData>('scrape-jobs', {
      redis: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD || undefined,
        db: parseInt(process.env.REDIS_DB || '0'),
      },
      defaultJobOptions: {
        removeOnComplete: 100, // Keep last 100 completed jobs
        removeOnFail: 50, // Keep last 50 failed jobs
        attempts: parseInt(process.env.QUEUE_RETRY_ATTEMPTS || '3'),
        backoff: {
          type: 'exponential',
          delay: parseInt(process.env.QUEUE_RETRY_DELAY || '5000'),
        },
      },
    });

    this.bulkScrapeQueue = new Bull<BulkScrapeJobData>('bulk-scrape-jobs', {
      redis: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD || undefined,
        db: parseInt(process.env.REDIS_DB || '0'),
      },
      defaultJobOptions: {
        removeOnComplete: 50,
        removeOnFail: 25,
        attempts: 2,
        backoff: {
          type: 'exponential',
          delay: 10000,
        },
      },
    });

    this.setupJobProcessors();
    this.setupEventHandlers();
  }

  /**
   * Setup job processors
   */
  private setupJobProcessors(): void {
    // Single scrape job processor
    this.scrapeQueue.process(
      parseInt(process.env.QUEUE_CONCURRENCY || '5'),
      async (job: Job<ScrapeJobData>) => {
        return this.processScrapeJob(job);
      }
    );

    // Bulk scrape job processor
    this.bulkScrapeQueue.process(1, async (job: Job<BulkScrapeJobData>) => {
      return this.processBulkScrapeJob(job);
    });
  }

  /**
   * Setup event handlers for monitoring
   */
  private setupEventHandlers(): void {
    // Single scrape queue events
    this.scrapeQueue.on('completed', (job, result) => {
      logger.info(`Scrape job ${job.id} completed`, {
        jobId: job.id,
        url: job.data.url,
        processingTime: result.processingTime,
      });
    });

    this.scrapeQueue.on('failed', (job, err) => {
      logger.error(`Scrape job ${job.id} failed`, {
        jobId: job.id,
        url: job.data.url,
        error: err.message,
        attempts: job.attemptsMade,
      });
    });

    // Bulk scrape queue events
    this.bulkScrapeQueue.on('completed', (job, result) => {
      logger.info(`Bulk scrape job ${job.id} completed`, {
        jobId: job.id,
        batchId: job.data.batchId,
        totalUrls: job.data.urls.length,
      });
    });

    this.bulkScrapeQueue.on('failed', (job, err) => {
      logger.error(`Bulk scrape job ${job.id} failed`, {
        jobId: job.id,
        batchId: job.data.batchId,
        error: err.message,
      });
    });

    this.bulkScrapeQueue.on('progress', (job, progress) => {
      logger.debug(`Bulk scrape job ${job.id} progress: ${progress}%`, {
        jobId: job.id,
        batchId: job.data.batchId,
        progress,
      });
    });
  }

  /**
   * Process single scrape job
   */
  private async processScrapeJob(job: Job<ScrapeJobData>): Promise<ScrapeJobResult> {
    const startTime = Date.now();
    const { url, enableAI = false, jobId } = job.data;

    try {
      logger.info(`Processing scrape job ${jobId} for URL: ${url}`);

      // Update job progress
      await job.progress(10);

      // Perform scraping
      const result = await scraperService.scrape(url);
      await job.progress(80);

      // Apply AI enhancement if requested
      let finalResult = result;
      if (enableAI) {
        // Note: AI enhancement would be implemented here
        // For now, we'll use the basic result
        await job.progress(95);
      }

      await job.progress(100);

      const processingTime = Date.now() - startTime;
      
      return {
        url,
        success: true,
        data: finalResult,
        processingTime,
        timestamp: Date.now(),
      };
    } catch (error) {
      const processingTime = Date.now() - startTime;
      logger.error(`Scrape job ${jobId} failed for URL ${url}:`, error);
      
      return {
        url,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        processingTime,
        timestamp: Date.now(),
      };
    }
  }

  /**
   * Process bulk scrape job
   */
  private async processBulkScrapeJob(job: Job<BulkScrapeJobData>): Promise<any> {
    const { urls, enableAI = false, batchId, concurrency = 3 } = job.data;
    const results: ScrapeJobResult[] = [];
    const startTime = Date.now();

    logger.info(`Processing bulk scrape job for batch ${batchId} with ${urls.length} URLs`);

    try {
      // Create individual scrape jobs for each URL
      const scrapeJobs: Promise<ScrapeJobResult>[] = [];
      
      for (let i = 0; i < urls.length; i += concurrency) {
        const batch = urls.slice(i, i + concurrency);
        
        const batchPromises = batch.map(async (url, index) => {
          const jobId = `${batchId}-${i + index}`;
          
          // Add individual scrape job
          const scrapeJob = await this.addScrapeJob({
            url,
            enableAI,
            jobId,
            batchId,
            priority: 1, // Lower priority for bulk jobs
          });

          // Wait for job completion
          return scrapeJob.finished() as Promise<ScrapeJobResult>;
        });

        const batchResults = await Promise.allSettled(batchPromises);
        
        batchResults.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            results.push(result.value);
          } else {
            results.push({
              url: batch[index],
              success: false,
              error: result.reason?.message || 'Job failed',
              processingTime: 0,
              timestamp: Date.now(),
            });
          }
        });

        // Update progress
        const progress = Math.round((results.length / urls.length) * 100);
        await job.progress(progress);
        
        // Store intermediate results
        await this.storeBulkResults(batchId, results);
      }

      const processingTime = Date.now() - startTime;
      const successCount = results.filter(r => r.success).length;
      const failureCount = results.length - successCount;

      logger.info(`Bulk scrape job completed for batch ${batchId}`, {
        total: urls.length,
        successful: successCount,
        failed: failureCount,
        processingTime,
      });

      return {
        batchId,
        total: urls.length,
        successful: successCount,
        failed: failureCount,
        results,
        processingTime,
        timestamp: Date.now(),
      };
    } catch (error) {
      logger.error(`Bulk scrape job failed for batch ${batchId}:`, error);
      throw error;
    }
  }

  /**
   * Add single scrape job to queue
   */
  async addScrapeJob(data: ScrapeJobData, options?: JobOptions): Promise<Job<ScrapeJobData>> {
    const jobOptions: JobOptions = {
      priority: data.priority || 5,
      delay: 0,
      ...options,
    };

    const job = await this.scrapeQueue.add('scrape', data, jobOptions);
    logger.info(`Added scrape job ${job.id} for URL: ${data.url}`);
    
    return job;
  }

  /**
   * Add bulk scrape job to queue
   */
  async addBulkScrapeJob(data: BulkScrapeJobData, options?: JobOptions): Promise<Job<BulkScrapeJobData>> {
    const job = await this.bulkScrapeQueue.add('bulk-scrape', data, options);
    logger.info(`Added bulk scrape job ${job.id} for batch: ${data.batchId}`);
    
    return job;
  }

  /**
   * Get job by ID
   */
  async getJob(jobId: string): Promise<Job | null> {
    const scrapeJob = await this.scrapeQueue.getJob(jobId);
    if (scrapeJob) return scrapeJob;
    
    const bulkJob = await this.bulkScrapeQueue.getJob(jobId);
    return bulkJob || null;
  }

  /**
   * Store bulk job results in Redis
   */
  private async storeBulkResults(batchId: string, results: ScrapeJobResult[]): Promise<void> {
    try {
      const key = `bulk-results:${batchId}`;
      await this.redis.setex(key, 86400, JSON.stringify(results)); // Store for 24 hours
    } catch (error) {
      logger.error(`Failed to store bulk results for batch ${batchId}:`, error);
    }
  }

  /**
   * Get bulk job results
   */
  async getBulkResults(batchId: string): Promise<ScrapeJobResult[] | null> {
    try {
      const key = `bulk-results:${batchId}`;
      const data = await this.redis.get(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      logger.error(`Failed to get bulk results for batch ${batchId}:`, error);
      return null;
    }
  }

  /**
   * Get queue statistics
   */
  async getQueueStats(): Promise<any> {
    const [scrapeWaiting, scrapeActive, scrapeCompleted, scrapeFailed] = await Promise.all([
      this.scrapeQueue.getWaiting(),
      this.scrapeQueue.getActive(),
      this.scrapeQueue.getCompleted(),
      this.scrapeQueue.getFailed(),
    ]);

    const [bulkWaiting, bulkActive, bulkCompleted, bulkFailed] = await Promise.all([
      this.bulkScrapeQueue.getWaiting(),
      this.bulkScrapeQueue.getActive(),
      this.bulkScrapeQueue.getCompleted(),
      this.bulkScrapeQueue.getFailed(),
    ]);

    return {
      scrapeQueue: {
        waiting: scrapeWaiting.length,
        active: scrapeActive.length,
        completed: scrapeCompleted.length,
        failed: scrapeFailed.length,
      },
      bulkScrapeQueue: {
        waiting: bulkWaiting.length,
        active: bulkActive.length,
        completed: bulkCompleted.length,
        failed: bulkFailed.length,
      },
    };
  }

  /**
   * Clean up old jobs
   */
  async cleanOldJobs(): Promise<void> {
    await this.scrapeQueue.clean(24 * 60 * 60 * 1000, 'completed'); // Clean completed jobs older than 24 hours
    await this.scrapeQueue.clean(7 * 24 * 60 * 60 * 1000, 'failed'); // Clean failed jobs older than 7 days
    
    await this.bulkScrapeQueue.clean(24 * 60 * 60 * 1000, 'completed');
    await this.bulkScrapeQueue.clean(7 * 24 * 60 * 60 * 1000, 'failed');
  }

  /**
   * Close queue connections
   */
  async close(): Promise<void> {
    await this.scrapeQueue.close();
    await this.bulkScrapeQueue.close();
  }
}

// Export singleton instance
export const scraperQueue = new ScraperQueueManager();
export default scraperQueue;
