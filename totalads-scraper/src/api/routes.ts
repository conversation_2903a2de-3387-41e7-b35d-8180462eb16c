/**
 * API routes for the scraper service
 */
import { Router } from "express";

import { combinedRateLimit } from "../middleware/rate-limit";
import { scraperController, ScrapeURLDataSchema } from "./scraper.controller";

const router = Router();

/**
 * @route POST /scrape
 * @desc Scrape a URL and return extracted data
 * @access Public
 */
router.post(
	"/",
	combinedRateLimit(), // Apply rate limiting
	async (req, res) => {
		try {
			// Validate request body
			ScrapeURLDataSchema.parse(req.body);
			// We're using the controller for actual implementation
			await scraperController.scrapeUrl(req, res);
		} catch (error) {
			res.status(400).json({
				success: false,
				error:
					error instanceof Error
						? error.message
						: "Invalid request data",
			});
		}
	},
);

/**
 * @route GET /health
 * @desc Health check endpoint
 * @access Public
 */
router.get("/health", (req, res) => {
	scraperController.healthCheck(req, res);
});

/**
 * @route GET /stats
 * @desc Get scraper statistics
 * @access Public
 */
router.get("/stats", (req, res) => {
	scraperController.getStats(req, res);
});

export default router;
