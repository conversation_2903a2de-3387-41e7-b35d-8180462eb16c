/**
 * API routes for the scraper service
 */
import { Router } from 'express';
import { expressAs<PERSON><PERSON>and<PERSON> } from 'totalads-shared';

import { scraper<PERSON>ontroller, ScrapeURLDataSchema } from './scraper.controller';

const router = Router();

/**
 * @route POST /scrape
 * @desc Scrape a URL and return extracted data
 * @access Public
 */
router.post(
	"/",
	expressAsyncHandler(
		async (validatedData, req, res) => {
			// We're using the controller for actual implementation
			await scraperController.scrapeUrl(req, res);
		},
		{
			validationSchema: ScrapeURLDataSchema,
			getValue: (req) => req.body,
		},
	),
);

/**
 * @route GET /health
 * @desc Health check endpoint
 * @access Public
 */
router.get("/health", (req, res) => {
	scraperController.healthCheck(req, res);
});

export default router;
