/**
 * Controller for bulk scraping operations
 */
import { Request, Response } from 'express';
import { z } from 'zod';
import { v4 as uuidv4 } from 'uuid';
import { scraperQueue } from '../queue/scraper-queue';
import { bulkJobRateLimiter, globalRateLimiter } from '../utils/redis-rate-limiter';
import logger from '../utils/logger';

// Validation schemas
export const BulkScrapeSchema = z.object({
  urls: z.array(z.string().url()).min(1).max(1000),
  enableAI: z.boolean().optional().default(false),
  concurrency: z.number().min(1).max(10).optional().default(3),
  metadata: z.record(z.any()).optional(),
});

export const JobStatusSchema = z.object({
  jobId: z.string(),
});

export const JobListSchema = z.object({
  page: z.number().min(1).optional().default(1),
  limit: z.number().min(1).max(100).optional().default(20),
  status: z.enum(['waiting', 'active', 'completed', 'failed']).optional(),
});

export class BulkScraperController {
  /**
   * Submit bulk scraping job
   */
  async submitBulkJob(req: Request, res: Response): Promise<void> {
    try {
      const { urls, enableAI = false, concurrency = 3, metadata } = req.body;
      const clientIp = req.ip || req.connection.remoteAddress || 'unknown';

      logger.info(`Bulk scrape request received from ${clientIp}`, {
        urlCount: urls.length,
        enableAI,
        concurrency,
      });

      // Check rate limits
      const globalLimit = await globalRateLimiter.checkLimit(clientIp);
      if (!globalLimit.allowed) {
        res.status(429).json({
          success: false,
          error: 'Rate limit exceeded',
          retryAfter: globalLimit.retryAfter,
        });
        return;
      }

      const bulkLimit = await bulkJobRateLimiter.checkLimit(clientIp);
      if (!bulkLimit.allowed) {
        res.status(429).json({
          success: false,
          error: 'Bulk job rate limit exceeded',
          retryAfter: bulkLimit.retryAfter,
        });
        return;
      }

      // Generate batch ID
      const batchId = uuidv4();

      // Submit bulk job
      const job = await scraperQueue.addBulkScrapeJob({
        urls,
        enableAI,
        batchId,
        concurrency,
        metadata: {
          ...metadata,
          clientIp,
          submittedAt: new Date().toISOString(),
        },
      });

      res.status(202).json({
        success: true,
        data: {
          jobId: job.id,
          batchId,
          status: 'queued',
          urlCount: urls.length,
          estimatedCompletionTime: this.estimateCompletionTime(urls.length, concurrency),
        },
        message: 'Bulk scraping job submitted successfully',
      });
    } catch (error) {
      logger.error('Error submitting bulk job:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to submit bulk scraping job',
      });
    }
  }

  /**
   * Get job status
   */
  async getJobStatus(req: Request, res: Response): Promise<void> {
    try {
      const { jobId } = req.params;

      const job = await scraperQueue.getJob(jobId);
      if (!job) {
        res.status(404).json({
          success: false,
          error: 'Job not found',
        });
        return;
      }

      const state = await job.getState();
      const progress = job.progress();
      const data = job.data;

      let results = null;
      if (state === 'completed' && data.batchId) {
        results = await scraperQueue.getBulkResults(data.batchId);
      }

      res.status(200).json({
        success: true,
        data: {
          jobId: job.id,
          batchId: data.batchId || null,
          status: state,
          progress,
          createdAt: new Date(job.timestamp).toISOString(),
          processedOn: job.processedOn ? new Date(job.processedOn).toISOString() : null,
          finishedOn: job.finishedOn ? new Date(job.finishedOn).toISOString() : null,
          attemptsMade: job.attemptsMade,
          failedReason: job.failedReason,
          results,
          metadata: data.metadata,
        },
      });
    } catch (error) {
      logger.error('Error getting job status:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get job status',
      });
    }
  }

  /**
   * List jobs with pagination
   */
  async listJobs(req: Request, res: Response): Promise<void> {
    try {
      const { page = 1, limit = 20, status } = req.query;
      const offset = (Number(page) - 1) * Number(limit);

      // Get queue statistics
      const stats = await scraperQueue.getQueueStats();

      // Get jobs based on status filter
      let jobs: any[] = [];
      if (status) {
        switch (status) {
          case 'waiting':
            jobs = await scraperQueue['scrapeQueue'].getWaiting(offset, offset + Number(limit) - 1);
            break;
          case 'active':
            jobs = await scraperQueue['scrapeQueue'].getActive(offset, offset + Number(limit) - 1);
            break;
          case 'completed':
            jobs = await scraperQueue['scrapeQueue'].getCompleted(offset, offset + Number(limit) - 1);
            break;
          case 'failed':
            jobs = await scraperQueue['scrapeQueue'].getFailed(offset, offset + Number(limit) - 1);
            break;
        }
      } else {
        // Get all jobs (mix of different statuses)
        const [waiting, active, completed, failed] = await Promise.all([
          scraperQueue['scrapeQueue'].getWaiting(0, 4),
          scraperQueue['scrapeQueue'].getActive(0, 4),
          scraperQueue['scrapeQueue'].getCompleted(0, 4),
          scraperQueue['scrapeQueue'].getFailed(0, 4),
        ]);
        jobs = [...waiting, ...active, ...completed, ...failed].slice(offset, offset + Number(limit));
      }

      const jobsData = await Promise.all(
        jobs.map(async (job) => {
          const state = await job.getState();
          return {
            jobId: job.id,
            batchId: job.data.batchId || null,
            status: state,
            progress: job.progress(),
            createdAt: new Date(job.timestamp).toISOString(),
            processedOn: job.processedOn ? new Date(job.processedOn).toISOString() : null,
            finishedOn: job.finishedOn ? new Date(job.finishedOn).toISOString() : null,
            urlCount: Array.isArray(job.data.urls) ? job.data.urls.length : 1,
            enableAI: job.data.enableAI || false,
          };
        })
      );

      res.status(200).json({
        success: true,
        data: {
          jobs: jobsData,
          pagination: {
            page: Number(page),
            limit: Number(limit),
            total: stats.scrapeQueue.waiting + stats.scrapeQueue.active + 
                   stats.scrapeQueue.completed + stats.scrapeQueue.failed,
          },
          stats,
        },
      });
    } catch (error) {
      logger.error('Error listing jobs:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to list jobs',
      });
    }
  }

  /**
   * Cancel a job
   */
  async cancelJob(req: Request, res: Response): Promise<void> {
    try {
      const { jobId } = req.params;

      const job = await scraperQueue.getJob(jobId);
      if (!job) {
        res.status(404).json({
          success: false,
          error: 'Job not found',
        });
        return;
      }

      const state = await job.getState();
      if (state === 'completed' || state === 'failed') {
        res.status(400).json({
          success: false,
          error: 'Cannot cancel completed or failed job',
        });
        return;
      }

      await job.remove();

      res.status(200).json({
        success: true,
        message: 'Job cancelled successfully',
      });
    } catch (error) {
      logger.error('Error cancelling job:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to cancel job',
      });
    }
  }

  /**
   * Retry a failed job
   */
  async retryJob(req: Request, res: Response): Promise<void> {
    try {
      const { jobId } = req.params;

      const job = await scraperQueue.getJob(jobId);
      if (!job) {
        res.status(404).json({
          success: false,
          error: 'Job not found',
        });
        return;
      }

      const state = await job.getState();
      if (state !== 'failed') {
        res.status(400).json({
          success: false,
          error: 'Only failed jobs can be retried',
        });
        return;
      }

      await job.retry();

      res.status(200).json({
        success: true,
        message: 'Job retry initiated successfully',
      });
    } catch (error) {
      logger.error('Error retrying job:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retry job',
      });
    }
  }

  /**
   * Get queue statistics
   */
  async getQueueStats(req: Request, res: Response): Promise<void> {
    try {
      const stats = await scraperQueue.getQueueStats();

      res.status(200).json({
        success: true,
        data: stats,
      });
    } catch (error) {
      logger.error('Error getting queue stats:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get queue statistics',
      });
    }
  }

  /**
   * Health check for bulk scraper
   */
  async healthCheck(req: Request, res: Response): Promise<void> {
    try {
      const stats = await scraperQueue.getQueueStats();
      const isHealthy = stats.scrapeQueue.active < 100 && stats.bulkScrapeQueue.active < 10;

      res.status(isHealthy ? 200 : 503).json({
        success: isHealthy,
        data: {
          status: isHealthy ? 'healthy' : 'overloaded',
          queues: stats,
          timestamp: new Date().toISOString(),
        },
      });
    } catch (error) {
      logger.error('Error in bulk scraper health check:', error);
      res.status(503).json({
        success: false,
        error: 'Health check failed',
      });
    }
  }

  /**
   * Estimate completion time for bulk job
   */
  private estimateCompletionTime(urlCount: number, concurrency: number): string {
    const avgTimePerUrl = 5000; // 5 seconds average per URL
    const totalTime = (urlCount / concurrency) * avgTimePerUrl;
    const completionTime = new Date(Date.now() + totalTime);
    return completionTime.toISOString();
  }
}

export const bulkScraperController = new BulkScraperController();
