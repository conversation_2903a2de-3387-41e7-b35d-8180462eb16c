/**
 * API routes for bulk scraping operations
 */
import { Router } from "express";
import { expressAsyncHandler } from "totalads-shared";

import { bulkRateLimit, globalRateLimit } from "../middleware/rate-limit";
import {
	bulkScraperController,
	BulkScrapeSchema,
	JobListSchema,
	JobStatusSchema,
} from "./bulk-scraper.controller";

const router = Router();

/**
 * @route POST /scrape/bulk
 * @desc Submit bulk scraping job
 * @access Public
 */
router.post(
	"/bulk",
	bulkRateLimit(), // Apply bulk-specific rate limiting
	expressAsyncHandler(
		async (validatedData, req, res) => {
			await bulkScraperController.submitBulkJob(req, res);
		},
		{
			validationSchema: BulkScrapeSchema,
			getValue: (req) => req.body,
		},
	),
);

/**
 * @route GET /scrape/job/:jobId
 * @desc Get job status and results
 * @access Public
 */
router.get(
	"/job/:jobId",
	expressAsyncHandler(
		async (validatedData, req, res) => {
			await bulkScraperController.getJobStatus(req, res);
		},
		{
			validationSchema: JobStatusSchema,
			getValue: (req) => ({ jobId: req.params.jobId }),
		},
	),
);

/**
 * @route GET /scrape/jobs
 * @desc List all jobs with pagination
 * @access Public
 */
router.get(
	"/jobs",
	expressAsyncHandler(
		async (validatedData, req, res) => {
			await bulkScraperController.listJobs(req, res);
		},
		{
			validationSchema: JobListSchema,
			getValue: (req) => req.query,
		},
	),
);

/**
 * @route DELETE /scrape/job/:jobId
 * @desc Cancel a job
 * @access Public
 */
router.delete(
	"/job/:jobId",
	expressAsyncHandler(
		async (validatedData, req, res) => {
			await bulkScraperController.cancelJob(req, res);
		},
		{
			validationSchema: JobStatusSchema,
			getValue: (req) => ({ jobId: req.params.jobId }),
		},
	),
);

/**
 * @route POST /scrape/job/:jobId/retry
 * @desc Retry a failed job
 * @access Public
 */
router.post(
	"/job/:jobId/retry",
	expressAsyncHandler(
		async (validatedData, req, res) => {
			await bulkScraperController.retryJob(req, res);
		},
		{
			validationSchema: JobStatusSchema,
			getValue: (req) => ({ jobId: req.params.jobId }),
		},
	),
);

/**
 * @route GET /scrape/stats
 * @desc Get queue statistics
 * @access Public
 */
router.get("/stats", (req, res) => {
	bulkScraperController.getQueueStats(req, res);
});

/**
 * @route GET /scrape/health
 * @desc Health check for bulk scraper
 * @access Public
 */
router.get("/health", (req, res) => {
	bulkScraperController.healthCheck(req, res);
});

export default router;
