/**
 * Redis-based distributed rate limiter for scraping operations
 */
import { redisClient } from "totalads-shared";

import { createRateLimitError } from "./error-handler";
import logger from "./logger";

export interface RedisRateLimitConfig {
	windowMs: number;
	maxRequests: number;
	keyPrefix: string;
	blockDuration?: number;
}

export interface RateLimitResult {
	allowed: boolean;
	remaining: number;
	resetTime: number;
	retryAfter?: number;
}

export class RedisRateLimiter {
	private config: RedisRateLimitConfig;
	private redis;

	constructor(config: RedisRateLimitConfig) {
		this.config = {
			blockDuration: 60000, // 1 minute default block
			...config,
		};
		this.redis = redisClient;
	}

	/**
	 * Check if request is allowed and consume token if so
	 */
	async checkLimit(identifier: string): Promise<RateLimitResult> {
		const key = `${this.config.keyPrefix}:${identifier}`;
		const now = Date.now();
		const windowStart = now - this.config.windowMs;

		try {
			// Use Redis pipeline for atomic operations
			const pipeline = this.redis.pipeline();

			// Remove expired entries
			pipeline.zremrangebyscore(key, 0, windowStart);

			// Count current requests in window
			pipeline.zcard(key);

			// Add current request timestamp
			pipeline.zadd(key, now, `${now}-${Math.random()}`);

			// Set expiration for the key
			pipeline.expire(key, Math.ceil(this.config.windowMs / 1000));

			const results = await pipeline.exec();

			if (!results || !results[1] || results[1][1] === undefined) {
				throw new Error("Redis pipeline execution failed");
			}

			const currentCount = (results[1][1] as number) || 0;
			const allowed = currentCount < this.config.maxRequests;

			if (!allowed) {
				// Remove the request we just added since it's not allowed
				await this.redis.zrem(key, `${now}-${Math.random()}`);
			}

			const remaining = Math.max(
				0,
				this.config.maxRequests - currentCount - (allowed ? 1 : 0),
			);
			const resetTime = now + this.config.windowMs;

			const result: RateLimitResult = {
				allowed,
				remaining,
				resetTime,
			};

			if (!allowed) {
				result.retryAfter = Math.ceil(this.config.windowMs / 1000);
			}

			logger.debug(`Rate limit check for ${identifier}`, {
				key,
				currentCount,
				allowed,
				remaining,
				resetTime: new Date(resetTime).toISOString(),
			});

			return result;
		} catch (error) {
			logger.error("Redis rate limiter error:", error);
			// Fail open - allow request if Redis is down
			return {
				allowed: true,
				remaining: this.config.maxRequests - 1,
				resetTime: now + this.config.windowMs,
			};
		}
	}

	/**
	 * Get current rate limit status without consuming a token
	 */
	async getStatus(identifier: string): Promise<RateLimitResult> {
		const key = `${this.config.keyPrefix}:${identifier}`;
		const now = Date.now();
		const windowStart = now - this.config.windowMs;

		try {
			// Clean up expired entries and get count
			await this.redis.zremrangebyscore(key, 0, windowStart);
			const currentCount = await this.redis.zcard(key);

			const allowed = currentCount < this.config.maxRequests;
			const remaining = Math.max(
				0,
				this.config.maxRequests - currentCount,
			);
			const resetTime = now + this.config.windowMs;

			const result: RateLimitResult = {
				allowed,
				remaining,
				resetTime,
			};

			if (!allowed) {
				result.retryAfter = Math.ceil(this.config.windowMs / 1000);
			}

			return result;
		} catch (error) {
			logger.error("Redis rate limiter status check error:", error);
			return {
				allowed: true,
				remaining: this.config.maxRequests,
				resetTime: now + this.config.windowMs,
			};
		}
	}

	/**
	 * Reset rate limit for identifier
	 */
	async reset(identifier: string): Promise<void> {
		const key = `${this.config.keyPrefix}:${identifier}`;
		try {
			await this.redis.del(key);
			logger.info(`Rate limit reset for ${identifier}`);
		} catch (error) {
			logger.error(
				`Failed to reset rate limit for ${identifier}:`,
				error,
			);
		}
	}

	/**
	 * Block identifier for specified duration
	 */
	async block(identifier: string, duration?: number): Promise<void> {
		const blockKey = `${this.config.keyPrefix}:blocked:${identifier}`;
		const blockDuration = duration || this.config.blockDuration || 60000;

		try {
			await this.redis.setex(
				blockKey,
				Math.ceil(blockDuration / 1000),
				"1",
			);
			logger.warn(`Blocked ${identifier} for ${blockDuration}ms`);
		} catch (error) {
			logger.error(`Failed to block ${identifier}:`, error);
		}
	}

	/**
	 * Check if identifier is blocked
	 */
	async isBlocked(identifier: string): Promise<boolean> {
		const blockKey = `${this.config.keyPrefix}:blocked:${identifier}`;
		try {
			const blocked = await this.redis.exists(blockKey);
			return blocked === 1;
		} catch (error) {
			logger.error(
				`Failed to check block status for ${identifier}:`,
				error,
			);
			return false;
		}
	}

	/**
	 * Unblock identifier
	 */
	async unblock(identifier: string): Promise<void> {
		const blockKey = `${this.config.keyPrefix}:blocked:${identifier}`;
		try {
			await this.redis.del(blockKey);
			logger.info(`Unblocked ${identifier}`);
		} catch (error) {
			logger.error(`Failed to unblock ${identifier}:`, error);
		}
	}
}

// Default rate limiters for different use cases
export const globalRateLimiter = new RedisRateLimiter({
	windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || "60000"),
	maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || "10"),
	keyPrefix: "scraper:global",
});

export const domainRateLimiter = new RedisRateLimiter({
	windowMs: 60000, // 1 minute
	maxRequests: 5, // 5 requests per domain per minute
	keyPrefix: "scraper:domain",
});

export const bulkJobRateLimiter = new RedisRateLimiter({
	windowMs: 300000, // 5 minutes
	maxRequests: 3, // 3 bulk jobs per 5 minutes
	keyPrefix: "scraper:bulk",
});

export default RedisRateLimiter;
