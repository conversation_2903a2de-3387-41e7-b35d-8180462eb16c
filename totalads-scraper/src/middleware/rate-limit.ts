/**
 * Rate limiting middleware for scraper endpoints
 */
import { NextFunction, Request, Response } from "express";

import logger from "../utils/logger";
import {
	domainRateLimiter,
	globalRateLimiter,
} from "../utils/redis-rate-limiter";

export interface RateLimitOptions {
	skipSuccessfulRequests?: boolean;
	skipFailedRequests?: boolean;
	keyGenerator?: (req: Request) => string;
	onLimitReached?: (req: Request, res: Response) => void;
}

/**
 * Global rate limiting middleware
 */
export function globalRateLimit(options: RateLimitOptions = {}) {
	return async (req: Request, res: Response, next: NextFunction) => {
		try {
			const key = options.keyGenerator
				? options.keyGenerator(req)
				: req.ip || req.connection.remoteAddress || "unknown";

			const result = await globalRateLimiter.checkLimit(key);

			// Set rate limit headers
			res.set({
				"X-RateLimit-Limit":
					globalRateLimiter["config"].maxRequests.toString(),
				"X-RateLimit-Remaining": result.remaining.toString(),
				"X-RateLimit-Reset": new Date(result.resetTime).toISOString(),
			});

			if (!result.allowed) {
				if (result.retryAfter) {
					res.set("Retry-After", result.retryAfter.toString());
				}

				if (options.onLimitReached) {
					options.onLimitReached(req, res);
				}

				logger.warn(`Rate limit exceeded for ${key}`, {
					key,
					remaining: result.remaining,
					resetTime: new Date(result.resetTime).toISOString(),
				});

				res.status(429).json({
					success: false,
					error: "Too many requests",
					retryAfter: result.retryAfter,
					resetTime: new Date(result.resetTime).toISOString(),
				});
				return;
			}

			next();
		} catch (error) {
			logger.error("Rate limiting middleware error:", error);
			// Fail open - allow request if rate limiter fails
			next();
		}
	};
}

/**
 * Domain-specific rate limiting middleware
 */
export function domainRateLimit(options: RateLimitOptions = {}) {
	return async (req: Request, res: Response, next: NextFunction) => {
		try {
			const url = req.body.url || req.query.url;
			if (!url) {
				next();
				return;
			}

			const domain = new URL(url).hostname;
			const result = await domainRateLimiter.checkLimit(domain);

			// Set domain-specific rate limit headers
			res.set({
				"X-Domain-RateLimit-Limit":
					domainRateLimiter["config"].maxRequests.toString(),
				"X-Domain-RateLimit-Remaining": result.remaining.toString(),
				"X-Domain-RateLimit-Reset": new Date(
					result.resetTime,
				).toISOString(),
			});

			if (!result.allowed) {
				if (result.retryAfter) {
					res.set("Retry-After", result.retryAfter.toString());
				}

				logger.warn(`Domain rate limit exceeded for ${domain}`, {
					domain,
					remaining: result.remaining,
					resetTime: new Date(result.resetTime).toISOString(),
				});

				res.status(429).json({
					success: false,
					error: `Too many requests to domain ${domain}`,
					domain,
					retryAfter: result.retryAfter,
					resetTime: new Date(result.resetTime).toISOString(),
				});
				return;
			}

			next();
		} catch (error) {
			logger.error("Domain rate limiting middleware error:", error);
			// Fail open - allow request if rate limiter fails
			next();
		}
	};
}

/**
 * Combined rate limiting middleware (global + domain)
 */
export function combinedRateLimit(options: RateLimitOptions = {}) {
	return async (req: Request, res: Response, next: NextFunction) => {
		// Apply global rate limit first
		globalRateLimit(options)(req, res, (err) => {
			if (err || res.headersSent) {
				return;
			}

			// Then apply domain rate limit
			domainRateLimit(options)(req, res, next);
		});
	};
}

/**
 * IP-based key generator
 */
export const ipKeyGenerator = (req: Request): string => {
	return req.ip || req.connection.remoteAddress || "unknown";
};

/**
 * User-based key generator (if authentication is implemented)
 */
export const userKeyGenerator = (req: Request): string => {
	// This would be used if you have user authentication
	return (req as any).user?.id || req.ip || "unknown";
};

/**
 * Custom key generator for API keys
 */
export const apiKeyGenerator = (req: Request): string => {
	const apiKey = req.headers["x-api-key"] as string;
	return apiKey || req.ip || "unknown";
};
