#!/usr/bin/env node
/**
 * Script to clean up old jobs and maintain queue health
 */
import '../config/loadEnv';
import { scraperQueue } from '../queue/scraper-queue';
import { redisManager } from '../config/redis';
import logger from '../utils/logger';

interface CleanupOptions {
  dryRun?: boolean;
  maxAge?: number; // in hours
  batchSize?: number;
}

class JobCleanup {
  private options: CleanupOptions;

  constructor(options: CleanupOptions = {}) {
    this.options = {
      dryRun: false,
      maxAge: 24, // 24 hours default
      batchSize: 100,
      ...options,
    };
  }

  /**
   * Run the cleanup process
   */
  async run(): Promise<void> {
    try {
      logger.info('Starting job cleanup process...', this.options);

      // Test Redis connection
      const redisConnected = await redisManager.testConnection();
      if (!redisConnected) {
        throw new Error('Failed to connect to Redis');
      }

      // Get initial stats
      const initialStats = await scraperQueue.getQueueStats();
      logger.info('Initial queue stats:', initialStats);

      // Clean up jobs
      if (!this.options.dryRun) {
        await scraperQueue.cleanOldJobs();
        logger.info('Queue cleanup completed');
      } else {
        logger.info('Dry run - no jobs were actually cleaned');
      }

      // Clean up bulk results
      await this.cleanupBulkResults();

      // Get final stats
      const finalStats = await scraperQueue.getQueueStats();
      logger.info('Final queue stats:', finalStats);

      // Calculate cleanup summary
      const summary = this.calculateCleanupSummary(initialStats, finalStats);
      logger.info('Cleanup summary:', summary);

    } catch (error) {
      logger.error('Job cleanup failed:', error);
      throw error;
    }
  }

  /**
   * Clean up old bulk results from Redis
   */
  private async cleanupBulkResults(): Promise<void> {
    try {
      const redis = redisManager.getClient();
      const pattern = 'bulk-results:*';
      const keys = await redis.keys(pattern);

      let cleanedCount = 0;
      const maxAge = this.options.maxAge! * 60 * 60; // Convert hours to seconds

      for (const key of keys) {
        const ttl = await redis.ttl(key);
        
        // If TTL is less than remaining time or key has no expiration
        if (ttl === -1 || ttl > maxAge) {
          if (!this.options.dryRun) {
            await redis.del(key);
          }
          cleanedCount++;
        }
      }

      logger.info(`Cleaned up ${cleanedCount} bulk result entries`, {
        totalKeys: keys.length,
        cleanedCount,
        dryRun: this.options.dryRun,
      });
    } catch (error) {
      logger.error('Failed to cleanup bulk results:', error);
    }
  }

  /**
   * Calculate cleanup summary
   */
  private calculateCleanupSummary(initial: any, final: any): any {
    return {
      scrapeQueue: {
        completed: {
          before: initial.scrapeQueue.completed,
          after: final.scrapeQueue.completed,
          cleaned: initial.scrapeQueue.completed - final.scrapeQueue.completed,
        },
        failed: {
          before: initial.scrapeQueue.failed,
          after: final.scrapeQueue.failed,
          cleaned: initial.scrapeQueue.failed - final.scrapeQueue.failed,
        },
      },
      bulkScrapeQueue: {
        completed: {
          before: initial.bulkScrapeQueue.completed,
          after: final.bulkScrapeQueue.completed,
          cleaned: initial.bulkScrapeQueue.completed - final.bulkScrapeQueue.completed,
        },
        failed: {
          before: initial.bulkScrapeQueue.failed,
          after: final.bulkScrapeQueue.failed,
          cleaned: initial.bulkScrapeQueue.failed - final.bulkScrapeQueue.failed,
        },
      },
    };
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const options: CleanupOptions = {};

  // Parse command line arguments
  for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
      case '--dry-run':
        options.dryRun = true;
        break;
      case '--max-age':
        options.maxAge = parseInt(args[++i]);
        break;
      case '--batch-size':
        options.batchSize = parseInt(args[++i]);
        break;
      case '--help':
        console.log(`
Usage: node cleanup-jobs.js [options]

Options:
  --dry-run         Run without actually cleaning up jobs
  --max-age <hours> Maximum age of jobs to keep (default: 24)
  --batch-size <n>  Number of jobs to process in each batch (default: 100)
  --help            Show this help message
        `);
        process.exit(0);
    }
  }

  try {
    const cleanup = new JobCleanup(options);
    await cleanup.run();
    
    // Close connections
    await scraperQueue.close();
    await redisManager.closeConnections();
    
    logger.info('Job cleanup completed successfully');
    process.exit(0);
  } catch (error) {
    logger.error('Job cleanup failed:', error);
    process.exit(1);
  }
}

// Run if this file is executed directly
if (require.main === module) {
  main();
}

export default JobCleanup;
