{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T15:59:53.048Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751471993364_igi5tol2s\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T15:59:53.364Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T15:59:53.416Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T15:59:53.416Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:00:03.848Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751472004108_uu6aafvqx\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:00:04.108Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:00:04.144Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:00:04.144Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:00:42.507Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751472042727_oj13cd8ms\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:00:42.727Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:00:42.759Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:00:42.759Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:00:56.199Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751472056470_tgy47y712\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:00:56.470Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:00:56.505Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:00:56.505Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:01:09.887Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751472070081_eo4kqielp\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:01:10.081Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:01:10.111Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:01:10.111Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:01:29.446Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751472089658_ae8gpdqwm\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:01:29.658Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:01:29.696Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:01:29.696Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:25:44.286Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751473544567_yu3pt0h4i\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:25:44.567Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:25:44.607Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:25:44.607Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:25:56.757Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751473556961_tw8l7pe8w\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:25:56.961Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:25:56.994Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:25:56.994Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:26:21.361Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751473581595_n5c1y63fx\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:26:21.595Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:26:21.645Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:26:21.645Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:32:01.414Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751473921670_nw22z2yy5\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:32:01.670Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:32:01.700Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:32:01.700Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:33:25.186Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751474005358_u8vjj45b9\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:33:25.358Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:33:25.384Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:33:25.384Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:33:48.378Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751474028550_zapiavmk1\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:33:48.550Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:33:48.575Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:33:48.575Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:39:10.144Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751474350344_mrih0aqom\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:39:10.344Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:39:10.373Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:39:10.373Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:39:38.569Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751474378755_i05djtjdy\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:39:38.755Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:39:38.783Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:39:38.783Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:40:00.589Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751474400886_73uqhs3f4\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:40:00.886Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:40:00.930Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:40:00.931Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:41:37.960Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751474498269_k9kjrd20v\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:41:38.269Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:41:38.299Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:41:38.299Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:41:57.153Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751474517337_q9kltpiaq\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:41:57.337Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:41:57.399Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:41:57.399Z"}
