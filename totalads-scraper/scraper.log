{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T15:59:53.048Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751471993364_igi5tol2s\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T15:59:53.364Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T15:59:53.416Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T15:59:53.416Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:00:03.848Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751472004108_uu6aafvqx\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:00:04.108Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:00:04.144Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:00:04.144Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:00:42.507Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751472042727_oj13cd8ms\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:00:42.727Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:00:42.759Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:00:42.759Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:00:56.199Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751472056470_tgy47y712\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:00:56.470Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:00:56.505Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:00:56.505Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:01:09.887Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751472070081_eo4kqielp\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:01:10.081Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:01:10.111Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:01:10.111Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:01:29.446Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751472089658_ae8gpdqwm\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-07-02T16:01:29.658Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-07-02T16:01:29.696Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-07-02T16:01:29.696Z"}
